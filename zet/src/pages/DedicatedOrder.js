import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Card, CardBody, Row, Col, Container, Al<PERSON>, Modal,
  ModalHeader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
} from 'reactstrap';
import PaymentModal from './PaymentModal';
import DiscountAlert from "../components/DiscountAlert";

// API Base URL
const API_BASE_URL = '/api.php';

const DedicatedOrder = () => {
  // State for configurations
  const [modelOptions, setModelOptions] = useState([]);
  const [storageOptions, setStorageOptions] = useState([]);
  const [bandwidthOptions, setBandwidthOptions] = useState([]);
  const [locationOptions, setLocationOptions] = useState([]);
  const [subnetOptions, setSubnetOptions] = useState([]);
  const [osOptions, setOsOptions] = useState([]);
  const [serversNumberOptions, setServersNumberOptions] = useState([]);

  // State for selections
  const [selectedModel, setSelectedModel] = useState(null);
  const [selectedStorage, setSelectedStorage] = useState(null);
  const [selectedBandwidth, setSelectedBandwidth] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedSubnet, setSelectedSubnet] = useState(null);
  const [serversNumber, setServersNumber] = useState(1);
  const [selectedOS, setSelectedOS] = useState(null);
  const [termsAccepted, setTermsAccepted] = useState(false);

  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [modalTotalPrice, setModalTotalPrice] = useState(0);
  const [vatRate, setVatRate] = useState(0);
  const [vatAmount, setVatAmount] = useState(0);
  const [totalWithVat, setTotalWithVat] = useState(0);
  const [deliveryTime, setDeliveryTime] = useState('');


  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderId, setOrderId] = useState(null);
  const [invoiceId, setInvoiceId] = useState(null);
  const [generatingInvoice, setGeneratingInvoice] = useState(false);
  
  // State for client discounts
  const [activeDedicatedServers, setActiveDedicatedServers] = useState(0);
  const [discountTiers, setDiscountTiers] = useState([]);
  const [currentDiscount, setCurrentDiscount] = useState(0);

  // Temporary state for modal selections
  const [tempServersNumber, setTempServersNumber] = useState(1);
  const [tempSubnet, setTempSubnet] = useState(null);
  const [tempOS, setTempOS] = useState(null);

  const navigate = useNavigate();

  // Function to get token from session storage
  const getToken = useCallback(() => {
    return sessionStorage.getItem('token');
  }, []);

  const [inventoryStatus, setInventoryStatus] = useState({
    available: false,
    message: "Checking availability..."
  });

  // Generic API request function with error handling
  const fetchApi = useCallback(async (endpoint, params = {}, method = 'POST', body = null) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      // Construct the query string with any parameters
      const queryParams = new URLSearchParams({
        f: endpoint,
        ...params
      }).toString();

      const requestBody = body || { token };

      console.log(`Fetching ${endpoint} with params:`, params);

      const response = await fetch(`${API_BASE_URL}?${queryParams}`, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Get the raw text first to inspect it
      const rawText = await response.text();
      console.log(`Raw API response for ${endpoint}:`, rawText);

      // Try to parse the response as JSON
      try {
        return JSON.parse(rawText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        throw new Error(`Invalid JSON response: ${rawText.substring(0, 100)}...`);
      }
    } catch (err) {
      console.error(`API error (${endpoint}):`, err);
      throw err;
    }
  }, [getToken]);

  // Function to fetch RAM configuration details
  const fetchRamConfiguration = useCallback(async (configId) => {
    try {
      console.log(`Fetching RAM configuration for config_id: ${configId}`);
      const data = await fetchApi('get_ram_config', { config_id: configId });
      if (data && data.success && data.ram) {
        console.log(`Received RAM configuration: ${data.ram}`);
        return data.ram;
      } else if (data && data.error) {
        console.warn(`Error from RAM config API: ${data.error}`);
      }
      return null;
    } catch (err) {
      console.error(`Failed to fetch RAM configuration for config ID ${configId}:`, err);
      return null;
    }
  }, [fetchApi]);

  // Process the configuration data
  const processConfigData = useCallback((data) => {
    console.log('=== processConfigData called ===');
    console.log('Data received:', data);
    if (!data) return;

    // Set options and initial selections
    setModelOptions(data.models || []);
    setStorageOptions(data.storage || []);
    setBandwidthOptions(data.bandwidth || []);
    setLocationOptions(data.location || []);
    setSubnetOptions(data.subnet || []);
    setOsOptions(data.os || []);
    setServersNumberOptions(data.servers_number || []);

    // Set initial selections
    const initialModel = data.models?.find(model => model.checked);
    const initialStorage = data.storage?.find(storage => storage.checked);
    const initialBandwidth = data.bandwidth?.find(bandwidth => bandwidth.checked);
    const initialLocation = data.location?.find(location => location.checked);
    const initialSubnet = data.subnet?.find(subnet => subnet.checked);
    const initialOS = data.os?.find(os => os.checked);
    const initialServersNumber = data.servers_number?.find(num => num.checked);

    // Ensure RAM information is properly set for the model
    if (initialModel && !initialModel.ram && initialModel.ram_id) {
      // If ram_id is available but ram is not, try to fetch it from ram_configurations
      // This would typically be handled by the backend, but we can ensure it's displayed properly here
      console.log("RAM ID found but no RAM description:", initialModel.ram_id);
    }

    setSelectedModel(initialModel || null);
    setSelectedStorage(initialStorage || null);
    setSelectedBandwidth(initialBandwidth || null);
    setSelectedLocation(initialLocation || null);
    setSelectedSubnet(initialSubnet || null);
    setSelectedOS(initialOS || null);
    setServersNumber(initialServersNumber ? parseInt(initialServersNumber.id) : 1);

    // Use the delivery time from the API response
    console.log('API Response data:', data);
    // The API returns an array, so we need to access the first element
    const configData = Array.isArray(data) ? data[0] : data;
    console.log('Config data:', configData);

    // Set price
    setTotalPrice(configData.price || 0);

    console.log('Delivery time from API:', configData.delivery);
    if (configData.delivery) {
      console.log('Setting delivery time to:', configData.delivery);
      setDeliveryTime(configData.delivery);
    } else {
      console.log('No delivery time in API response, using fallback logic');
      // Fallback to calculating it if not provided
      if (initialLocation) {
        // Use the delivery_time from the location if available
        const locationDeliveryTime = initialLocation.delivery_time || "2 weeks";
        setDeliveryTime(locationDeliveryTime);

        setInventoryStatus({
          available: initialLocation.has_stock || false,
          message: initialLocation.stock || "No stock information"
        });
      } else {
        setDeliveryTime("2 weeks");
        setInventoryStatus({
          available: false,
          message: "Availability unknown"
        });
      }
    }

    // Set inventory status based on initial location (this will override the above if location exists)
    if (initialLocation) {
      setInventoryStatus({
        available: initialLocation.has_stock || false,
        message: initialLocation.stock || "No stock information"
      });
    }

    // Also update temp selections to match initial values
    setTempServersNumber(initialServersNumber ? parseInt(initialServersNumber.id) : 1);
    setTempSubnet(initialSubnet || null);
    setTempOS(initialOS || null);
  }, []);

  // Fetch configuration data from the API
  const fetchDedicatedOrderConfig = useCallback(async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      const data = await fetchApi('dedicatedorder', params);

      if (Array.isArray(data) && data.length > 0) {
        // Process models to ensure RAM information is properly displayed
        if (data[0].models && Array.isArray(data[0].models)) {
          // Process each model to ensure RAM information is available
          const modelsWithPromises = data[0].models.map(async (model) => {
            // If RAM is missing but we have a config_id, fetch RAM details
            if ((!model.ram || model.ram === '') && model.config_id) {
              console.log(`Model ${model.id} is missing RAM info, fetching from config_id: ${model.config_id}`);
              try {
                // Try to fetch RAM configuration
                const ramInfo = await fetchRamConfiguration(model.config_id);
                if (ramInfo) {
                  model.ram = ramInfo;
                } else {
                  model.ram = "RAM details unavailable";
                }
              } catch (err) {
                console.error(`Error fetching RAM for model ${model.id}:`, err);
                model.ram = "RAM configuration pending";
              }
            }
            return model;
          });
          
          // Wait for all RAM info fetches to complete
          data[0].models = await Promise.all(modelsWithPromises);
        }
        
        processConfigData(data[0]);
      } else {
        throw new Error('Invalid data structure received from API');
      }
    } catch (err) {
      setError(`Failed to fetch configuration data: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [fetchApi, processConfigData, fetchRamConfiguration]);

  // Fetch initial configuration on component mount
  // Fetch active dedicated servers count and discount tiers
  const fetchDedicatedServersCount = useCallback(async () => {
    try {
      const token = getToken();
      const response = await fetch("/api.php?f=dedicated_servers_count", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.error) {
        console.error("Error fetching dedicated server count:", data.error);
      } else {
        console.log("Discount data received:", data);
        setActiveDedicatedServers(data.count || 0);
        setDiscountTiers(Array.isArray(data.tiers) ? data.tiers : []);
        
        // Calculate current discount tier
        if (Array.isArray(data.tiers) && data.tiers.length > 0) {
          const sortedTiers = [...data.tiers].sort((a, b) => a.active_servers - b.active_servers);
          
          // Find current discount tier (highest tier that the user qualifies for)
          const currentTier = sortedTiers
            .filter(tier => data.count >= tier.active_servers)
            .pop();
          
          if (currentTier) {
            setCurrentDiscount(currentTier.discount);
          } else {
            setCurrentDiscount(0);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching dedicated server count:", error);
    }
  }, [getToken]);

  useEffect(() => {
    fetchDedicatedOrderConfig();
    fetchDedicatedServersCount();
  }, [fetchDedicatedOrderConfig, fetchDedicatedServersCount]);

  // Update selections for a specific category
  const updateSelection = useCallback(async (category, selection) => {
    console.log('updateSelection called:', category, selection);
    // Update the local state first for responsive UI
    switch (category) {
      case 'model':
        setSelectedModel(selection);
        break;
      case 'storage':
        setSelectedStorage(selection);
        break;
      case 'bandwidth':
        setSelectedBandwidth(selection);
        break;
      case 'location':
        setSelectedLocation(selection);
        // Delivery time will be updated from the API response in fetchDedicatedOrderConfig
        break;
      case 'subnet':
        setSelectedSubnet(selection);
        break;
      case 'os':
        setSelectedOS(selection);
        break;
      case 'servers_number':
        setServersNumber(parseInt(selection.id));
        break;
      default:
        break;
    }

    // Prepare parameters for the API request
    const params = {};

    // Add existing selections
    if (selectedModel) params.model_id = selectedModel.id;
    if (selectedStorage) params.storage_id = selectedStorage.id;
    if (selectedBandwidth) params.bandwidth_id = selectedBandwidth.id;
    if (selectedLocation) params.location_id = selectedLocation.id;
    if (selectedSubnet) params.subnet_id = selectedSubnet.id;
    if (selectedOS) params.os_id = selectedOS.id;
    if (serversNumber) params.servers_number = serversNumber;

    // Override with the new selection
    params[`${category}_id`] = selection.id;

    // Fetch updated configuration
    console.log('Calling fetchDedicatedOrderConfig with params:', params);
    try {
      await fetchDedicatedOrderConfig(params);
      console.log('fetchDedicatedOrderConfig completed successfully');
    } catch (err) {
      // The error will be handled in fetchDedicatedOrderConfig
      console.error("Error updating selection:", err);
    }
  }, [selectedModel, selectedStorage, selectedBandwidth, selectedLocation, selectedSubnet, selectedOS, serversNumber, fetchDedicatedOrderConfig]);

  // Event handlers for selection changes
  const handleModelChange = model => updateSelection('model', model);
  const handleStorageChange = storage => updateSelection('storage', storage);
  const handleBandwidthChange = bandwidth => updateSelection('bandwidth', bandwidth);
  const handleLocationChange = location => updateSelection('location', location);
  const handleSubnetChange = subnet => updateSelection('subnet', subnet);
  const handleOSChange = os => updateSelection('os', os);
  const handleServersNumberChange = number => updateSelection('servers_number', { id: number });

  // Toggle confirmation modal
  const toggleConfirmModal = () => {
    if (showConfirmModal) {
      // When closing the modal, reset temporary values
      setTempServersNumber(serversNumber);
      setTempSubnet(selectedSubnet);
      setTempOS(selectedOS);
    } else {
      // When opening the modal, initialize temp values with current selections
      setTempServersNumber(serversNumber);
      setTempSubnet(selectedSubnet);
      setTempOS(selectedOS);

      // Recalculate price with these initial temporary values
      updateModalPrice(serversNumber, selectedSubnet, selectedOS);

      console.log('=== MODAL PRICE CALCULATION ===');
      console.log(`Active dedicated servers: ${activeDedicatedServers}`);
      console.log(`Ordering ${serversNumber} new servers`);
      
      // Calculate base price per server (without the existing server count)
      let baseServerPrice = Number(totalPrice);
      
      // If totalPrice already includes multiple servers, get per-server price
      if (serversNumber > 1) {
        baseServerPrice = baseServerPrice / serversNumber;
        console.log(`Total price (${totalPrice}) divided by servers (${serversNumber}) = Base price per server: €${baseServerPrice.toFixed(2)}`);
      } else {
        console.log(`Base price per server: €${baseServerPrice.toFixed(2)}`);
      }
      
      // Calculate single server price with addons
      let singleServerPrice = baseServerPrice;
      
      // Add prices from existing selections if applicable (per server)
      if (selectedSubnet && selectedSubnet.price) {
        const subnetPrice = Number(selectedSubnet.price);
        singleServerPrice += subnetPrice;
        console.log(`Added subnet (${selectedSubnet.name}) price: €${subnetPrice.toFixed(2)}`);
      }

      if (selectedOS && selectedOS.price) {
        const osPrice = Number(selectedOS.price);
        singleServerPrice += osPrice;
        console.log(`Added OS (${selectedOS.name}) price: €${osPrice.toFixed(2)}`);
      }
      
      console.log(`Final price per server: €${singleServerPrice.toFixed(2)}`);
      
      // Calculate total price for all servers
      let totalBeforeDiscount = singleServerPrice * serversNumber;
      console.log(`Total before discount (${singleServerPrice.toFixed(2)} × ${serversNumber}): €${totalBeforeDiscount.toFixed(2)}`);
      
      // Calculate total servers (existing + new)
      const totalServerCount = activeDedicatedServers + serversNumber;
      console.log(`Total servers for discount calculation: ${activeDedicatedServers} (existing) + ${serversNumber} (new) = ${totalServerCount}`);
      
      // Apply appropriate discount based on total server count
      let finalPrice = totalBeforeDiscount;
      let appliedDiscount = 0;
      
      // Debug discount tiers
      if (discountTiers.length > 0) {
        console.log('Available discount tiers:');
        discountTiers.forEach(tier => {
          console.log(`- ${tier.active_servers}+ servers: ${tier.discount}% discount`);
        });
      } else {
        console.log('No discount tiers available, using fallback logic');
      }
      
      // Find applicable discount tier
      if (discountTiers.length > 0) {
        const sortedTiers = [...discountTiers].sort((a, b) => a.active_servers - b.active_servers);
        const applicableTier = sortedTiers
          .filter(tier => totalServerCount >= tier.active_servers)
          .pop();
        
        if (applicableTier) {
          appliedDiscount = applicableTier.discount;
          console.log(`DISCOUNT TIER APPLIED: ${appliedDiscount}% for ${totalServerCount} total servers`);
        } else {
          console.log(`No discount tier applies for ${totalServerCount} total servers`);
        }
      } else {
        // Fallback discount logic
        if (totalServerCount >= 10) {
          appliedDiscount = 20;
          console.log(`FALLBACK DISCOUNT: 20% for ${totalServerCount} total servers (≥10)`);
        } else if (totalServerCount >= 5) {
          appliedDiscount = 10;
          console.log(`FALLBACK DISCOUNT: 10% for ${totalServerCount} total servers (≥5)`);
        } else {
          console.log(`No fallback discount applies for ${totalServerCount} total servers (need 5+)`);
        }
      }
      
      // Apply discount
      if (appliedDiscount > 0) {
        const discountAmount = totalBeforeDiscount * (appliedDiscount / 100);
        finalPrice = totalBeforeDiscount - discountAmount;
        console.log(`Applied ${appliedDiscount}% discount (€${discountAmount.toFixed(2)})`);
      }
      
      // Force discount for testing if applicable servers but no discount applied
      if (totalServerCount >= 5 && appliedDiscount === 0) {
        console.log('ERROR: Should apply discount but did not. Forcing 5% minimum discount.');
        appliedDiscount = 5;
        finalPrice = totalBeforeDiscount * 0.95;
      }

      console.log(`Final price after discount: €${finalPrice.toFixed(2)}`);
      console.log('=== END MODAL PRICE CALCULATION ===');
      
      setModalTotalPrice(Math.round(finalPrice));
    }
    setShowConfirmModal(!showConfirmModal);
  };

  // Toggle payment modal
  const togglePaymentModal = () => {
    setShowPaymentModal(!showPaymentModal);
  };

  // Handle order button click
  const handleOrderClick = () => {
    // Validate required selections
    if (!selectedModel || !selectedStorage || !selectedBandwidth || !selectedLocation) {
      setError('Please select all required options before ordering.');
      return;
    }

    // Open the confirmation modal
    toggleConfirmModal();
  };

  // Handle temporary selection changes in the modal
  const handleTempSubnetChange = (subnet) => {
    setTempSubnet(subnet);
    updateModalPrice(tempServersNumber, subnet, tempOS);
  };

  const handleTempOSChange = (os) => {
    setTempOS(os);
    updateModalPrice(tempServersNumber, tempSubnet, os);
  };

  const handleTempServersNumberChange = (number) => {
    const parsedNumber = parseInt(number);
    setTempServersNumber(parsedNumber);
    updateModalPrice(parsedNumber, tempSubnet, tempOS);
  };

  // Calculate and update modal price based on selections
  const updateModalPrice = (servers, subnet, os) => {
    // Start with the base server price (without considering number of servers yet)
    // Extract the base server price from totalPrice (which might already include multiple servers)
    let baseServerPrice = Number(totalPrice);
    
    // If current state already includes multiple servers, divide to get per-server price
    if (serversNumber > 1) {
      baseServerPrice = baseServerPrice / serversNumber;
    }
    
    console.log('--- PRICE CALCULATION DEBUG ---');
    console.log(`Base server price (from state): €${baseServerPrice.toFixed(2)}`);
    
    // Calculate single server price with addons
    let singleServerPrice = baseServerPrice;
    
    // Add price for subnet if applicable (per server)
    if (subnet && subnet.price) {
      const subnetPrice = Number(subnet.price);
      singleServerPrice += subnetPrice;
      console.log(`Added subnet price: €${subnetPrice.toFixed(2)}, new single server price: €${singleServerPrice.toFixed(2)}`);
    }

    // Add price for OS if applicable (per server)
    if (os && os.price) {
      const osPrice = Number(os.price);
      singleServerPrice += osPrice;
      console.log(`Added OS price: €${osPrice.toFixed(2)}, new single server price: €${singleServerPrice.toFixed(2)}`);
    }
    
    // Calculate total price for all servers before discount
    let totalPriceBeforeDiscount = singleServerPrice * servers;
    console.log(`Single server price: €${singleServerPrice.toFixed(2)}`);
    console.log(`Total for ${servers} servers (before discount): €${totalPriceBeforeDiscount.toFixed(2)}`);

    // Calculate total servers (existing + new order) for discount determination
    const totalServers = activeDedicatedServers + servers;
    console.log(`Total servers for discount: ${activeDedicatedServers} (existing) + ${servers} (new) = ${totalServers}`);
    
    // Find the applicable discount tier based on total servers
    let appliedDiscount = 0;
    
    // Debug the discount tiers
    console.log(`Discount tiers available: ${discountTiers.length}`);
    if (discountTiers.length > 0) {
      console.log('Available discount tiers:');
      discountTiers.forEach(tier => {
        console.log(`- ${tier.active_servers}+ servers: ${tier.discount}% discount`);
      });
      
      // Sort tiers by required server count
      const sortedTiers = [...discountTiers].sort((a, b) => a.active_servers - b.active_servers);
      
      // Find the highest tier that applies with the total servers
      const applicableTier = sortedTiers
        .filter(tier => totalServers >= tier.active_servers)
        .pop();
      
      if (applicableTier) {
        appliedDiscount = applicableTier.discount;
        console.log(`✓ DISCOUNT APPLIED: ${appliedDiscount}% from tier requiring ${applicableTier.active_servers} servers`);
      } else {
        console.log(`✗ NO DISCOUNT TIER APPLIES for ${totalServers} total servers`);
      }
    } else {
      // Fallback to the old discount logic if no discount tiers were loaded
      console.log(`No discount tiers loaded, using fallback logic`);
      if (totalServers >= 10) {
        appliedDiscount = 20; // 20% discount for 10+ servers
        console.log(`✓ FALLBACK DISCOUNT: 20% for ${totalServers} servers (≥10)`);
      } else if (totalServers >= 5) {
        appliedDiscount = 10; // 10% discount for 5-9 servers
        console.log(`✓ FALLBACK DISCOUNT: 10% for ${totalServers} servers (≥5)`);
      } else {
        console.log(`✗ NO FALLBACK DISCOUNT for ${totalServers} servers (need 5+ for discount)`);
      }
    }
    
    // Apply the discount
    let finalPrice = totalPriceBeforeDiscount;
    if (appliedDiscount > 0) {
      const discountAmount = totalPriceBeforeDiscount * (appliedDiscount / 100);
      finalPrice = totalPriceBeforeDiscount - discountAmount;
      console.log(`Applied ${appliedDiscount}% discount (€${discountAmount.toFixed(2)})`);
      console.log(`Final price after discount: €${finalPrice.toFixed(2)}`);
    } else {
      console.log(`No discount applied. Final price: €${finalPrice.toFixed(2)}`);
    }
    
    // Force applying minimum discount for testing
    if (totalServers >= 5 && appliedDiscount === 0) {
      console.log('ERROR: Discount should apply but did not. Forcing minimum discount.');
      appliedDiscount = 5;
      finalPrice = totalPriceBeforeDiscount * 0.95; // Force 5% discount
    }

    console.log('--- END PRICE CALCULATION ---');
    setModalTotalPrice(Math.round(finalPrice));
  };

  // Note: Invoice generation is now handled within the placededicatedorder endpoint

  // Place an order with the API
  const placeDedicatedOrder = async () => {
    setLoading(true);
    setError(null);
  
    try {
      // FIRST - Fetch VAT information
      console.log("Step 1: Retrieving VAT information");
      const vatResponse = await fetchApi('get_vat_rate');
      
      // Extract and calculate VAT details
      let calculatedVatRate = 0;
      let calculatedVatAmount = 0;
      let calculatedTotalWithVat = modalTotalPrice;
      
      if (vatResponse.success) {
        calculatedVatRate = vatResponse.vat_exempt ? 0 : vatResponse.vat_rate;
        calculatedVatAmount = (modalTotalPrice * calculatedVatRate) / 100;
        calculatedTotalWithVat = modalTotalPrice + calculatedVatAmount;
        
        console.log(`VAT information retrieved: Rate=${calculatedVatRate}%, Amount=${calculatedVatAmount.toFixed(2)}, Total=${calculatedTotalWithVat.toFixed(2)}`);
      } else {
        console.warn("Could not retrieve VAT information, using 0%");
      }
      
      // Update state with VAT information
      setVatRate(calculatedVatRate);
      setVatAmount(calculatedVatAmount);
      setTotalWithVat(calculatedTotalWithVat);
  
      // SECOND - Place order (which now creates both order and invoice in one transaction, like cloud servers)
      console.log("Step 2: Placing order and creating invoice");
      
      const orderParams = {
        model_id: selectedModel.id,
        storage_id: selectedStorage.id,
        bandwidth_id: selectedBandwidth.id,
        location_id: selectedLocation.id,
        subnet_id: tempSubnet.id,
        os_id: tempOS.id,
        servers_number: tempServersNumber,
        price: modalTotalPrice,
        config_id: selectedModel.config_id || 1
      };
  
      const orderResponse = await fetchApi('placededicatedorder', orderParams);
  
      if (!orderResponse.success) {
        throw new Error(`Order placement failed: ${orderResponse.error || 'Unknown error'}`);
      }
  
      // Extract order and invoice IDs from the response
      const orderId = orderResponse.orderid;
      const invoiceId = orderResponse.invoice_id;
      const responseVatRate = orderResponse.vat_rate || calculatedVatRate;
      const responseVatAmount = orderResponse.vat_amount || calculatedVatAmount;
      const responseTotalWithVat = orderResponse.total_with_vat || calculatedTotalWithVat;
      
      setOrderId(orderId);
      setInvoiceId(invoiceId);
      
      // Update VAT information from response if available
      setVatRate(responseVatRate);
      setVatAmount(responseVatAmount);
      setTotalWithVat(responseTotalWithVat);
      
      console.log(`Order and invoice created successfully: Order ID=${orderId}, Invoice ID=${invoiceId}, Servers=${tempServersNumber}`);
      console.log(`Final VAT details: Rate=${responseVatRate}%, Amount=${responseVatAmount}, Total=${responseTotalWithVat}`);

      // Close confirmation modal and open payment modal
      toggleConfirmModal();
      setTimeout(() => {
        togglePaymentModal();
      }, 100);
  
    } catch (err) {
      console.error('Order Placement Error:', err);
      setError(err.message || 'An unexpected error occurred while placing your order');
    } finally {
      setLoading(false);
    }
  };

const fetchVatRate = async () => {
  try {
    const response = await fetchApi('get_vat_rate');
    if (response.success) {
      return {
        vatRate: response.vat_rate,
        country: response.country,
        vatExempt: response.vat_exempt
      };
    }
    return { vatRate: 0, country: '', vatExempt: false };
  } catch (err) {
    console.error("Error fetching VAT rate:", err);
    return { vatRate: 0, country: '', vatExempt: false };
  }
};

  // Handle order submission
  const handleOrder = () => {
    if (!termsAccepted) {
      setError('Please accept the Terms of Service before proceeding.');
      return;
    }

    // Apply the temporary selections to the actual state
    setServersNumber(tempServersNumber);
    setSelectedSubnet(tempSubnet);
    setSelectedOS(tempOS);

    // Place the order
    placeDedicatedOrder();
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    // Close the payment modal
    togglePaymentModal();

    // Navigate to the invoice page if we have an invoice ID
    if (invoiceId) {
      navigate(`/billing/invoices/${invoiceId}`);
    } else {
      // Otherwise navigate to the server list/dashboard
      navigate('/');
    }
  };

  // Render loading spinner
  if (loading && !selectedModel) {
    return (
      <Container fluid className="p-0">
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">New Dedicated Server</li>
            </ol>
          </div>
        </div>
        <div className="text-center my-5 py-5">
          <Spinner color="primary" />
        </div>
      </Container>
    );
  }

  // Render error state
  if (error && !selectedModel) {
    return (
      <Container fluid className="p-0">
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">New Dedicated Server</li>
            </ol>
          </div>
        </div>
        <Alert color="danger" className="my-4">
          <i className="fa fa-exclamation-circle me-2"></i>
          <strong>Error:</strong> {error}
          <div className="mt-3">
            <Button color="primary" onClick={() => fetchDedicatedOrderConfig()}>
              Try Again
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="p-0">
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">New Dedicated Server</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <DiscountAlert />

      {error && (
        <Alert color="danger" className="mb-4">
          <i className="fa fa-exclamation-circle me-2"></i>
          <strong>Error:</strong> {error}
        </Alert>
      )}

      <Card className="shadow-sm mb-4">
        <CardBody>
          <h2 className="card-title text-primary mb-4">Configure Your Dedicated Server</h2>

          <Row className="g-4 mb-4">
            {/* Server Model Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-server text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Server Model
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {modelOptions.map(model => (
                      <div key={model.id} className="position-relative">
                        <input
                          type="radio"
                          id={`model_${model.id}`}
                          name="serverModel"
                          className="better-radio"
                          checked={selectedModel && selectedModel.id === model.id}
                          onChange={() => handleModelChange(model)}
                        />
                        <label
                          htmlFor={`model_${model.id}`}
                          className={`list-group-item d-flex flex-column p-3 ${selectedModel && selectedModel.id === model.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{model.cpu}</div>
                          {model.cores && (
                            <div className="text-muted small">{model.cores}</div>
                          )}
                          <div className="text-muted small">{model.ram || "RAM information not available"}</div>
                          <div className="text-success">
                            {Number(model.price) > 0 ? `+€${model.price}` : <span className="text-success">Included</span>}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Storage Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-hdd-o text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Storage
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {storageOptions.map(storage => (
                      <div key={storage.id} className="position-relative">
                        <input
                          type="radio"
                          id={`storage_${storage.id}`}
                          name="storage"
                          className="better-radio"
                          checked={selectedStorage && selectedStorage.id === storage.id}
                          onChange={() => handleStorageChange(storage)}
                        />
                        <label
                          htmlFor={`storage_${storage.id}`}
                          className={`list-group-item d-flex flex-column p-3 ${selectedStorage && selectedStorage.id === storage.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{storage.name}</div>
                          <div className="text-muted small">
                            {Number(storage.price) === 0 ? <span className="text-success">Included</span> : `+€${storage.price}`}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Bandwidth Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-tachometer text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Bandwidth
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {bandwidthOptions.map(bandwidth => (
                      <div key={bandwidth.id} className="position-relative">
                        <input
                          type="radio"
                          id={`bandwidth_${bandwidth.id}`}
                          name="bandwidth"
                          className="better-radio"
                          checked={selectedBandwidth && selectedBandwidth.id === bandwidth.id}
                          onChange={() => handleBandwidthChange(bandwidth)}
                        />
                        <label
                          htmlFor={`bandwidth_${bandwidth.id}`}
                          className={`list-group-item d-flex flex-column p-3 ${selectedBandwidth && selectedBandwidth.id === bandwidth.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{bandwidth.name}</div>
                          <div className="text-muted small">
                            {Number(bandwidth.price) === 0 ? <span className="text-success">Included</span> : `+€${bandwidth.price}`}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Location Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-map-marker text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Location
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {locationOptions.map(location => (
                      <div key={location.id} className="position-relative">
                        <input
                          type="radio"
                          id={`location_${location.id}`}
                          name="location"
                          className="better-radio"
                          checked={selectedLocation && selectedLocation.id === location.id}
                          onChange={() => handleLocationChange(location)}
                        />
                        <label
                          htmlFor={`location_${location.id}`}
                          className={`list-group-item d-flex align-items-center p-3 ${selectedLocation && selectedLocation.id === location.id ? 'selected' : ''}`}
                        >
                          <i className={`flag flag-${location.flag}`} style={{ marginRight: '10px' }}></i>
                          <div>
                            <div className="fw-bold">{location.name}</div>
                            {location.stock && (
                              <div className="text-muted small">
                                <span style={{ color: location.stockcolor }}>
                                  {location.stock}
                                </span>
                                {location.delivery_time && location.delivery_time !== "15 minutes" && (
                                  <span className="ms-2" style={{ color: location.stockcolor }}>
                                    • {location.delivery_time} delivery
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>

          {/* Order Summary and Action Buttons */}
          <Card className="border-0 bg-light shadow-sm p-4">
            <Row className="align-items-center">
              <Col xs={12} md={6}>
                <h3 className="mb-0">
                  Total Price: <span className="text-success">€{totalPrice}/mo</span>
                </h3>
                {serversNumber > 1 && (
                  <p className="text-muted mb-0">
                    €{Math.round(totalPrice / serversNumber)}
                  </p>
                )}
                <p className="text-muted mb-0">Delivery: {deliveryTime}</p>
                
                {/* Discount display logic */}
                {(() => {
                  const totalServers = activeDedicatedServers + serversNumber;
                  
                  // Display current discount if applicable
                  if (currentDiscount > 0) {
                    return (
                      <p className="text-success mb-0">
                        <i className="fa fa-check-circle me-1"></i>
                        Your account qualifies for a {currentDiscount}% volume discount
                      </p>
                    );
                  }
                  
                  // Check if adding new servers would qualify for a discount
                  if (discountTiers.length > 0) {
                    const sortedTiers = [...discountTiers].sort((a, b) => a.active_servers - b.active_servers);
                    const lowestTier = sortedTiers[0];
                    
                    if (lowestTier && totalServers >= lowestTier.active_servers) {
                      // Will qualify for discount with new servers
                      return (
                        <p className="text-success mb-0">
                          <i className="fa fa-check-circle me-1"></i>
                          You will qualify for a {lowestTier.discount}% discount with this order
                          ({activeDedicatedServers} existing + {serversNumber} new)
                        </p>
                      );
                    }
                    
                    // Show how many more servers needed for discount
                    if (lowestTier) {
                      const serversNeeded = lowestTier.active_servers - totalServers;
                      if (serversNeeded > 0) {
                        return (
                          <p className="text-muted mb-0">
                            <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                            Need {serversNeeded} more server(s) to qualify for {lowestTier.discount}% discount
                          </p>
                        );
                      }
                    }
                  } else if (totalServers >= 5) {
                    // Fallback discount messaging
                    const discountRate = totalServers >= 10 ? 20 : 10;
                    return (
                      <p className="text-success mb-0">
                        <i className="fa fa-check-circle me-1"></i>
                        You will qualify for a {discountRate}% discount with this order
                      </p>
                    );
                  } else if (activeDedicatedServers > 0) {
                    // Just show active servers count
                    return (
                      <p className="text-muted mb-0">
                        <i className="fa fa-info-circle me-1"></i>
                        You have {activeDedicatedServers} active servers
                      </p>
                    );
                  }
                  
                  return null;
                })()}
              </Col>
              <Col xs={12} md={6} className="text-md-end mt-3 mt-md-0 d-flex justify-content-end">
                <Button className="btn btn-success" size="lg" onClick={handleOrderClick} disabled={loading}>
                  {loading ? (
                    <>
                      <Spinner size="sm" className="me-2" />
                      Order Now
                    </>
                  ) : (
                    <>
                      <i className="fa fa-check-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                      Order Now
                    </>
                  )}
                </Button>
              </Col>
            </Row>
          </Card>
        </CardBody>
      </Card>

      {/* Order Confirmation Modal */}
      <Modal
        isOpen={showConfirmModal}
        toggle={toggleConfirmModal}
        className="order-summary-modal"
        backdropClassName="fixed-backdrop"
        backdrop={true}
        zIndex={9999}
        size="lg"
      >
        <ModalHeader toggle={toggleConfirmModal}>Order Summary</ModalHeader>
        <ModalBody>
          {error && (
            <Alert color="danger" className="mb-4">
              <i className="fa fa-exclamation-circle me-2"></i>
              <strong>Error:</strong> {error}
            </Alert>
          )}

          {selectedModel && selectedStorage && selectedBandwidth && selectedLocation && (
            <>
              <h5 className="mb-3">Selected Configuration</h5>
              <div className="table-responsive mb-4">
                <table className="table card-table table-vcenter text-nowrap">
                  <tbody>
                    <tr>
                      <td><b>Server Model</b></td>
                      <td>{selectedModel.cpu || selectedModel.name}</td>
                    </tr>
                    <tr>
                      <td><b>CPU Cores</b></td>
                      <td>{selectedModel.cores || "Core information not available"}</td>
                    </tr>
                    <tr>
                      <td><b>Server RAM</b></td>
                      <td>{selectedModel.ram || "RAM information not available"}</td>
                    </tr>
                    <tr>
                      <td><b>Storage</b></td>
                      <td>{selectedStorage.name}</td>
                    </tr>
                    <tr>
                      <td><b>Bandwidth</b></td>
                      <td>{selectedBandwidth.name}</td>
                    </tr>
                    <tr>
                      <td><b>Location</b></td>
                      <td><i className={`flag flag-${selectedLocation.flag}`}></i> {selectedLocation.name}</td>
                    </tr>
                    <tr>
                      <td><b>Servers Number</b></td>
                      <td>
                        <select
                          className="form-select form-control"
                          value={tempServersNumber}
                          onChange={(e) => handleTempServersNumberChange(parseInt(e.target.value))}
                        >
                          {serversNumberOptions.map(option => (
                            <option key={option.id} value={option.id}>
                              {option.name}
                            </option>
                          ))}
                        </select>
          
                      </td>
                    </tr>
                    <tr>
                      <td><b>IPv4 Subnet</b></td>
                      <td>
                      <select
  className="form-select form-control"
  value={tempSubnet ? String(tempSubnet.id) : ''}
  onChange={(e) => {
    const selectedSubnet = subnetOptions.find(subnet => subnet.id == e.target.value);
    if (selectedSubnet) {
      handleTempSubnetChange(selectedSubnet);
    }
  }}
>
  <option value="" disabled>Select IPv4 Subnet</option>
  {subnetOptions.map(subnet => (
    <option key={subnet.id} value={String(subnet.id)}>
      {subnet.name} {Number(subnet.price) > 0 ? `(+€${subnet.price})` : '(Included)'}
    </option>
  ))}
</select>
                      </td>
                    </tr>
                    <tr>
                      <td><b>Operating System</b></td>
                      <td>
                      <select
  className="form-select form-control"
  value={tempOS ? String(tempOS.id) : ''}
  onChange={(e) => {
    const selectedOS = osOptions.find(os => os.id == e.target.value);
    if (selectedOS) {
      handleTempOSChange(selectedOS);
    }
  }}
>
  <option value="" disabled>Select Operating System</option>
  {osOptions.map(os => (
    <option key={os.id} value={String(os.id)}>
      {os.name} {Number(os.price) > 0 ? `(+€${os.price})` : '(Included)'}
    </option>
  ))}
</select>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div className="card border-0 bg-light shadow-sm p-3 mb-3">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    {/* Show both total price and per-server price when ordering multiple servers */}
                    <h4 className="mb-0">
                      Total Price: <span className="text-success">€{modalTotalPrice}/mo</span>
                    </h4>
                    
                    {/* Display discount information */}
                    {(() => {
                      const totalServerCount = activeDedicatedServers + tempServersNumber;
                      let discountRate = 0;
                      
                      // Calculate applicable discount
                      if (discountTiers.length > 0) {
                        const sortedTiers = [...discountTiers].sort((a, b) => a.active_servers - b.active_servers);
                        const applicableTier = sortedTiers
                          .filter(tier => totalServerCount >= tier.active_servers)
                          .pop();
                        
                        if (applicableTier) {
                          discountRate = applicableTier.discount;
                        }
                      } else if (totalServerCount >= 5) {
                        discountRate = totalServerCount >= 10 ? 20 : 10;
                      }
                      
                      return discountRate > 0 ? (
                        <p className="text-success mb-0">
                          <strong>{discountRate}% discount applied</strong> ({totalServerCount} total servers)
                        </p>
                      ) : null;
                    })()}
                    
                    {tempServersNumber > 1 && (
                      <p className="text-muted mb-0">
                        €{Math.round(modalTotalPrice / tempServersNumber)}/server
                      </p>
                    )}
                    <p className="text-muted mb-0">Delivery: {deliveryTime}</p>
                    
                    {/* Calculate total servers and applicable discount */}
                    {(() => {
                      const totalServers = activeDedicatedServers + tempServersNumber;
                      
                      // Find applicable discount tier
                      let discountMessage = null;
                      if (discountTiers.length > 0) {
                        const sortedTiers = [...discountTiers].sort((a, b) => a.active_servers - b.active_servers);
                        const applicableTier = sortedTiers
                          .filter(tier => totalServers >= tier.active_servers)
                          .pop();
                        
                        if (applicableTier) {
                          discountMessage = (
                            <p className="text-success mb-0">
                              <i className="fa fa-check-circle me-1"></i>
                              {applicableTier.discount}% discount applied ({activeDedicatedServers} existing + {tempServersNumber} new = {totalServers} servers)
                            </p>
                          );
                        }
                      } else if (totalServers >= 5) {
                        // Fallback discount display
                        const discountRate = totalServers >= 10 ? 20 : 10;
                        discountMessage = (
                          <p className="text-success mb-0">
                            <i className="fa fa-check-circle me-1"></i>
                            {discountRate}% discount applied ({activeDedicatedServers} existing + {tempServersNumber} new = {totalServers} servers)
                          </p>
                        );
                      }
                      
                      return discountMessage;
                    })()}
                  </div>
                </div>
              </div>
            </>
          )}
        </ModalBody>
        <ModalFooter className="d-flex flex-column p-3">
          {/* Top row with terms of service */}
          <div className="w-100 mb-3">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <input
                type="checkbox"
                id="agreeTerms"
                checked={termsAccepted}
                onChange={() => setTermsAccepted(!termsAccepted)}
                style={{
                  marginRight: '5px',
                  marginLeft: '0',
                  marginTop: '0',
                  marginBottom: '0',
                  padding: '0',
                  position: 'relative',
                  float: 'none'
                }}
                required
              />
              I accept the <a href="https://zetservers.com/tos.php" target="_blank" style={{ marginLeft: '3px', color: '#1e88e5' }}>Terms of Service</a>
            </div>
          </div>

          {/* Bottom row with buttons */}
          <div className="d-flex justify-content-between w-100">
            <Button color="secondary" onClick={toggleConfirmModal} disabled={loading}>Cancel</Button>
            <Button
              color="success"
              onClick={handleOrder}
              disabled={!termsAccepted || loading || !tempSubnet || !tempOS}
            >
              {loading ? (
                <Spinner size="sm" className="me-2" />
              ) : (
                'Place Order'
              )}
            </Button>
          </div>
        </ModalFooter>
      </Modal>

      {/* Payment Modal */}
      <PaymentModal
  isOpen={showPaymentModal}
  toggle={togglePaymentModal}
  totalAmount={modalTotalPrice}
  onComplete={handlePaymentComplete}
  orderId={orderId}
  invoiceId={invoiceId}
  generatingInvoice={generatingInvoice}

  vatRate={vatRate}
  vatAmount={vatAmount}
  totalWithVat={totalWithVat}
  serversCount={tempServersNumber}
  pricePerServer={tempServersNumber > 1 ? Math.round(modalTotalPrice / tempServersNumber) : null}
/>

    </Container>
  );
};

export default DedicatedOrder;